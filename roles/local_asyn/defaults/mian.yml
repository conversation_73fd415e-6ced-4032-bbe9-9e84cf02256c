base_dir: "/etc/thestack/conf/local_asyn"
service_host: {{ vip }}
repos:
  - name: "hci_asyn"
    git_repo: "http://************/hci/hci_asyn.git"
    version: "master"
  - name: "hci_api"
    git_repo: "http://************/hci/hci_api.git"
    version: "master"
  - name: "hci_db"
    git_repo: "http://************/hci/hci_db.git"
    version: "master"
python_paths:
  - "/root/apps/hci/hci_api"
  - "/root/apps/hci/hci_db"
run_command: "export PYTHONPATH=/root/apps/hci/hci_api:/root/apps/hci/hci_db && cd /root/apps/hci/hci_asyn && nohup python agent_runner.py agent --queue queue_{{ ansible_host }} > /root/apps/hci/hci_asyn/agent.log 2>&1 &"
