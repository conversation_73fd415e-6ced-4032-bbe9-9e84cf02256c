# yaml-language-server: $schema=https://raw.githubusercontent.com/ansible/ansible-lint/main/src/ansiblelint/schemas/ansible.json#/$defs/tasks
---
- name: 配置项
  shell: docker service ls

- name: 创建项目基础目录
  file:
    path: "{{ base_dir }}"
    state: directory
    mode: '0755'

- name: 从Git仓库拉取代码
  git:
    repo: "{{ item.git_repo }}"
    dest: "{{ base_dir }}/{{ item.name }}"
    version: "{{ item.version }}"
    force: yes
  loop: "{{ repos }}"

- name: 创建配置目录
  file:
    path: "{{ base_dir }}/hci_asyn/config"
    state: directory
    mode: '0755'

- name: 生成配置文件
  template:
    src: "../templates/settings.py.j2"
    dest: "{{ base_dir }}/hci_asyn/config/settings.py"
    mode: '0644'

- name: 启动项目服务
  shell: "{{ run_command }}"
  async: 10
  poll: 0
