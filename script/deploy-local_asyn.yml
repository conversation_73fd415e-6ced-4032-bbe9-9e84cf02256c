---
# - name: Create multiple directories
#   file:
#     path: "/etc/thestack/conf/fluentd"
#     state: directory
#     mode: '0755'


- name: deploy local_asyn
  hosts: deploy
  vars:
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install local_asyn
      include_role:
        name: "../roles/local_asyn"
      when:  is_remove == 'false'

    - name: uninstall local_asyn
      shell: docker stack rm local_asyn
      when: "is_remove == 'true'"
      run_once: true
